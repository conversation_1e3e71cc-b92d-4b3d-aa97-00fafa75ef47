package com.example.controller;

import com.example.config.TestSecurityConfig;
import com.example.dto.request.MaterialRequest;
import com.example.dto.response.MaterialDTO;
import com.example.dto.response.PageResponse;
import com.example.service.MaterialService;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.context.annotation.Import;
import org.springframework.http.MediaType;
import org.springframework.security.test.context.support.WithMockUser;
import org.springframework.test.web.servlet.MockMvc;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;
import static org.springframework.security.test.web.servlet.request.SecurityMockMvcRequestPostProcessors.csrf;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

@WebMvcTest(MaterialController.class)
@Import(TestSecurityConfig.class)
class MaterialControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @MockBean
    private MaterialService materialService;

    @Autowired
    private ObjectMapper objectMapper;

    private MaterialRequest materialRequest;
    private MaterialDTO materialDTO;

    @BeforeEach
    void setUp() {
        // 创建材料请求
        materialRequest = new MaterialRequest();
        materialRequest.setMaterialName("机油");
        materialRequest.setSpecification("5W-30");
        materialRequest.setUnit("升");
        materialRequest.setUnitPrice(new BigDecimal("50.00"));
        materialRequest.setInventory(100);
        materialRequest.setCategory("润滑油");

        // 创建材料DTO
        materialDTO = new MaterialDTO();
        materialDTO.setMaterialId(1L);
        materialDTO.setMaterialName("机油");
        materialDTO.setSpecification("5W-30");
        materialDTO.setUnit("升");
        materialDTO.setUnitPrice(new BigDecimal("50.00"));
        materialDTO.setInventory(100);
        materialDTO.setCategory("润滑油");
        materialDTO.setStatus(1);
        materialDTO.setCreateTime(LocalDateTime.now());
        materialDTO.setUpdateTime(LocalDateTime.now());
    }

    @Test
    @WithMockUser(roles = "ADMIN")
    void testGetMaterials_Success() throws Exception {
        // Given
        List<MaterialDTO> materials = Arrays.asList(materialDTO);
        PageResponse<MaterialDTO> pageResponse = PageResponse.success(materials,
                new PageResponse.PageInfo(1, 20, 1L, 1));
        when(materialService.getMaterials(isNull(), isNull(), any())).thenReturn(pageResponse);

        // When & Then
        mockMvc.perform(get("/materials"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.message").value("获取材料列表成功"))
                .andExpect(jsonPath("$.data.data").isArray())
                .andExpect(jsonPath("$.data.data[0].materialId").value(1))
                .andExpect(jsonPath("$.data.data[0].materialName").value("机油"));

        verify(materialService).getMaterials(isNull(), isNull(), any());
    }

    @Test
    @WithMockUser(roles = "TECHNICIAN")
    void testGetMaterials_WithFilters() throws Exception {
        // Given
        List<MaterialDTO> materials = Arrays.asList(materialDTO);
        PageResponse<MaterialDTO> pageResponse = PageResponse.success(materials,
                new PageResponse.PageInfo(1, 10, 1L, 1));
        when(materialService.getMaterials(eq("机油"), eq("润滑油"), any())).thenReturn(pageResponse);

        // When & Then
        mockMvc.perform(get("/materials")
                        .param("page", "1")
                        .param("size", "10")
                        .param("search", "机油")
                        .param("category", "润滑油"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data.pageInfo.pageSize").value(10));

        verify(materialService).getMaterials(eq("机油"), eq("润滑油"), any());
    }

    @Test
    @WithMockUser(roles = "ADMIN")
    void testGetMaterial_Success() throws Exception {
        // Given
        when(materialService.getMaterial(1L)).thenReturn(materialDTO);

        // When & Then
        mockMvc.perform(get("/materials/1"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.message").value("获取材料详情成功"))
                .andExpect(jsonPath("$.data.materialId").value(1))
                .andExpect(jsonPath("$.data.materialName").value("机油"))
                .andExpect(jsonPath("$.data.specification").value("5W-30"));

        verify(materialService).getMaterial(1L);
    }

    @Test
    @WithMockUser(roles = "ADMIN")
    void testAddMaterial_Success() throws Exception {
        // Given
        when(materialService.addMaterial(any(MaterialRequest.class))).thenReturn(materialDTO);

        // When & Then
        mockMvc.perform(post("/materials")
                        .with(csrf())
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(materialRequest)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.message").value("添加材料成功"))
                .andExpect(jsonPath("$.data.materialId").value(1))
                .andExpect(jsonPath("$.data.materialName").value("机油"));

        verify(materialService).addMaterial(any(MaterialRequest.class));
    }

    @Test
    @WithMockUser(roles = "ADMIN")
    void testUpdateMaterial_Success() throws Exception {
        // Given
        when(materialService.updateMaterial(eq(1L), any(MaterialRequest.class))).thenReturn(materialDTO);

        // When & Then
        mockMvc.perform(put("/materials/1")
                        .with(csrf())
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(materialRequest)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.message").value("更新材料信息成功"))
                .andExpect(jsonPath("$.data.materialId").value(1));

        verify(materialService).updateMaterial(eq(1L), any(MaterialRequest.class));
    }

    @Test
    @WithMockUser(roles = "ADMIN")
    void testDeleteMaterial_Success() throws Exception {
        // Given
        doNothing().when(materialService).deleteMaterial(1L);

        // When & Then
        mockMvc.perform(delete("/materials/1")
                        .with(csrf()))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.message").value("删除材料成功"));

        verify(materialService).deleteMaterial(1L);
    }

    @Test
    @WithMockUser(roles = "ADMIN")
    void testGetCategories_Success() throws Exception {
        // Given
        List<String> categories = Arrays.asList("润滑油", "滤清器", "轮胎");
        when(materialService.getCategories()).thenReturn(categories);

        // When & Then
        mockMvc.perform(get("/materials/categories"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.message").value("获取材料分类成功"))
                .andExpect(jsonPath("$.data").isArray())
                .andExpect(jsonPath("$.data[0]").value("润滑油"));

        verify(materialService).getCategories();
    }

    @Test
    @WithMockUser(roles = "USER") // 错误的角色
    void testGetMaterials_Forbidden() throws Exception {
        // When & Then
        mockMvc.perform(get("/materials"))
                .andExpect(status().isForbidden());

        verify(materialService, never()).getMaterials(any(), any(), any());
    }

    @Test
    @WithMockUser(roles = "TECHNICIAN") // 技师不能添加材料
    void testAddMaterial_Forbidden() throws Exception {
        // When & Then
        mockMvc.perform(post("/materials")
                        .with(csrf())
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(materialRequest)))
                .andExpect(status().isForbidden());

        verify(materialService, never()).addMaterial(any(MaterialRequest.class));
    }

    @Test
    void testGetMaterials_Unauthorized() throws Exception {
        // When & Then
        mockMvc.perform(get("/materials"))
                .andExpect(status().isUnauthorized());

        verify(materialService, never()).getMaterials(any(), any(), any());
    }

    @Test
    @WithMockUser(roles = "ADMIN")
    void testAddMaterial_InvalidInput() throws Exception {
        // Given
        materialRequest.setMaterialName(null); // 缺少必填字段

        // When & Then
        mockMvc.perform(post("/materials")
                        .with(csrf())
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(materialRequest)))
                .andExpect(status().isBadRequest());

        verify(materialService, never()).addMaterial(any(MaterialRequest.class));
    }

    @Test
    @WithMockUser(roles = "ADMIN")
    void testAddMaterial_MissingUnit() throws Exception {
        // Given
        materialRequest.setUnit(null);

        // When & Then
        mockMvc.perform(post("/materials")
                        .with(csrf())
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(materialRequest)))
                .andExpect(status().isBadRequest());

        verify(materialService, never()).addMaterial(any(MaterialRequest.class));
    }

    @Test
    @WithMockUser(roles = "ADMIN")
    void testAddMaterial_InvalidPrice() throws Exception {
        // Given
        materialRequest.setUnitPrice(new BigDecimal("0")); // 价格必须大于0

        // When & Then
        mockMvc.perform(post("/materials")
                        .with(csrf())
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(materialRequest)))
                .andExpect(status().isBadRequest());

        verify(materialService, never()).addMaterial(any(MaterialRequest.class));
    }

    @Test
    @WithMockUser(roles = "ADMIN")
    void testUpdateMaterial_InvalidInput() throws Exception {
        // Given
        materialRequest.setInventory(null); // 缺少必填字段

        // When & Then
        mockMvc.perform(put("/materials/1")
                        .with(csrf())
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(materialRequest)))
                .andExpect(status().isBadRequest());

        verify(materialService, never()).updateMaterial(anyLong(), any(MaterialRequest.class));
    }

    @Test
    void testGetMaterial_InvalidId() throws Exception {
        // When & Then
        mockMvc.perform(get("/materials/invalid"))
                .andExpect(status().isBadRequest());

        verify(materialService, never()).getMaterial(anyLong());
    }

    @Test
    @WithMockUser(roles = "ADMIN")
    void testDeleteMaterial_InvalidId() throws Exception {
        // When & Then
        mockMvc.perform(delete("/materials/invalid")
                        .with(csrf()))
                .andExpect(status().isBadRequest());

        verify(materialService, never()).deleteMaterial(anyLong());
    }
}
