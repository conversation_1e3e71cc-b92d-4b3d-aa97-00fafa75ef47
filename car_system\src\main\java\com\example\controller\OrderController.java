package com.example.controller;

import com.example.dto.request.OrderRequest;
import com.example.dto.request.OrderCompletionRequest;
import com.example.dto.request.OrderFeedbackRequest;
import com.example.dto.request.OrderStatusUpdateRequest;
import com.example.dto.request.MaterialUsageRequest;
import com.example.dto.response.ApiResponse;
import com.example.dto.response.OrderDTO;
import com.example.security.JwtAuthenticationFilter;
import com.example.service.OrderService;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 维修工单控制器
 */
@RestController
@RequestMapping("/orders")
public class OrderController {

    @Autowired
    private OrderService orderService;

    /**
     * 提交维修工单
     */
    @PostMapping
    @PreAuthorize("hasRole('USER')")
    public ApiResponse<OrderDTO> submitOrder(@Valid @RequestBody OrderRequest request,
                                           HttpServletRequest httpRequest) {
        Long userId = getCurrentUserId(httpRequest);
        OrderDTO order = orderService.submitOrder(userId, request);
        return ApiResponse.success("工单提交成功", order);
    }

    /**
     * 获取工单详情
     */
    @GetMapping("/{id}")
    public ApiResponse<OrderDTO> getOrder(@PathVariable Long id,
                                        HttpServletRequest request) {
        JwtAuthenticationFilter.UserPrincipal principal = getCurrentUserPrincipal(request);
        OrderDTO order = orderService.getOrder(id, principal.getUserId(), principal.getUserType());
        return ApiResponse.success("获取工单详情成功", order);
    }

    /**
     * 更新工单状态（管理员专用）
     */
    @PatchMapping("/{id}/status")
    @PreAuthorize("hasRole('ADMIN')")
    public ApiResponse<Void> updateOrderStatus(@PathVariable Long id,
                                             @Valid @RequestBody OrderStatusUpdateRequest request) {
        orderService.updateOrderStatus(id, request);
        return ApiResponse.success("工单状态更新成功", null);
    }

    /**
     * 提交工单反馈
     */
    @PostMapping("/{id}/feedback")
    @PreAuthorize("hasRole('USER')")
    public ApiResponse<Void> submitFeedback(@PathVariable Long id,
                                          @Valid @RequestBody OrderFeedbackRequest request,
                                          HttpServletRequest httpRequest) {
        Long userId = getCurrentUserId(httpRequest);
        orderService.submitFeedback(id, userId, request);
        return ApiResponse.success("反馈提交成功", null);
    }

    /**
     * 获取工单状态历史
     */
    @GetMapping("/{id}/history")
    public ApiResponse<List<Object>> getOrderHistory(@PathVariable Long id,
                                                   HttpServletRequest request) {
        JwtAuthenticationFilter.UserPrincipal principal = getCurrentUserPrincipal(request);
        List<Object> history = orderService.getOrderHistory(id, principal.getUserId(), principal.getUserType());
        return ApiResponse.success("获取工单历史成功", history);
    }

    /**
     * 获取工单技师分配信息
     */
    @GetMapping("/{id}/assignments")
    public ApiResponse<List<Object>> getOrderAssignments(@PathVariable Long id,
                                                        HttpServletRequest request) {
        JwtAuthenticationFilter.UserPrincipal principal = getCurrentUserPrincipal(request);
        List<Object> assignments = orderService.getOrderAssignments(id, principal.getUserId(), principal.getUserType());
        return ApiResponse.success("获取技师分配信息成功", assignments);
    }

    /**
     * 技师接受工单
     */
    @PatchMapping("/{id}/accept")
    @PreAuthorize("hasRole('TECHNICIAN')")
    public ApiResponse<Void> acceptOrder(@PathVariable Long id,
                                       HttpServletRequest request) {
        Long technicianId = getCurrentUserId(request);
        orderService.acceptOrder(id, technicianId);
        return ApiResponse.success("工单接受成功", null);
    }

    /**
     * 技师拒绝工单
     */
    @PatchMapping("/{id}/reject")
    @PreAuthorize("hasRole('TECHNICIAN')")
    public ApiResponse<Void> rejectOrder(@PathVariable Long id,
                                       @RequestBody RejectRequest request,
                                       HttpServletRequest httpRequest) {
        Long technicianId = getCurrentUserId(httpRequest);
        orderService.rejectOrder(id, technicianId, request.getReason());
        return ApiResponse.success("工单拒绝成功", null);
    }

    /**
     * 开始维修工作
     */
    @PatchMapping("/{id}/start")
    @PreAuthorize("hasRole('TECHNICIAN')")
    public ApiResponse<Void> startWork(@PathVariable Long id,
                                     HttpServletRequest request) {
        Long technicianId = getCurrentUserId(request);
        orderService.startWork(id, technicianId);
        return ApiResponse.success("开始维修工作", null);
    }

    /**
     * 完成维修工作
     */
    @PatchMapping("/{id}/complete")
    @PreAuthorize("hasRole('TECHNICIAN')")
    public ApiResponse<Void> completeWork(@PathVariable Long id,
                                        @Valid @RequestBody OrderCompletionRequest request,
                                        HttpServletRequest httpRequest) {
        Long technicianId = getCurrentUserId(httpRequest);
        orderService.completeWork(id, technicianId, request);
        return ApiResponse.success("维修工作完成", null);
    }

    /**
     * 添加材料使用记录
     */
    @PostMapping("/{id}/materials")
    @PreAuthorize("hasRole('TECHNICIAN')")
    public ApiResponse<Void> addMaterialUsage(@PathVariable Long id,
                                            @Valid @RequestBody MaterialUsageRequest request,
                                            HttpServletRequest httpRequest) {
        Long technicianId = getCurrentUserId(httpRequest);
        orderService.addMaterialUsage(id, technicianId, request);
        return ApiResponse.success("材料使用记录添加成功", null);
    }

    /**
     * 获取工单材料使用记录
     */
    @GetMapping("/{id}/materials")
    public ApiResponse<List<Object>> getOrderMaterials(@PathVariable Long id,
                                                      HttpServletRequest request) {
        JwtAuthenticationFilter.UserPrincipal principal = getCurrentUserPrincipal(request);
        List<Object> materials = orderService.getOrderMaterials(id, principal.getUserId(), principal.getUserType());
        return ApiResponse.success("获取材料使用记录成功", materials);
    }

    /**
     * 获取当前用户ID
     */
    private Long getCurrentUserId(HttpServletRequest request) {
        JwtAuthenticationFilter.UserPrincipal principal =
            (JwtAuthenticationFilter.UserPrincipal) SecurityContextHolder.getContext()
                .getAuthentication().getPrincipal();
        return principal.getUserId();
    }

    /**
     * 获取当前用户主体信息
     */
    private JwtAuthenticationFilter.UserPrincipal getCurrentUserPrincipal(HttpServletRequest request) {
        return (JwtAuthenticationFilter.UserPrincipal) SecurityContextHolder.getContext()
            .getAuthentication().getPrincipal();
    }

    /**
     * 拒绝工单请求内部类
     */
    public static class RejectRequest {
        private String reason;

        public RejectRequest() {}

        public String getReason() {
            return reason;
        }

        public void setReason(String reason) {
            this.reason = reason;
        }
    }
}
