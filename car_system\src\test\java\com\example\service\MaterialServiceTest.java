package com.example.service;

import com.example.dto.request.MaterialRequest;
import com.example.dto.response.MaterialDTO;
import com.example.dto.response.PageResponse;
import com.example.entity.Material;
import com.example.exception.BusinessException;
import com.example.exception.ResourceNotFoundException;
import com.example.repository.MaterialRepository;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;


import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class MaterialServiceTest {

    @Mock
    private MaterialRepository materialRepository;

    @InjectMocks
    private MaterialService materialService;

    private Material testMaterial;
    private MaterialRequest materialRequest;

    @BeforeEach
    void setUp() {
        // 创建测试材料
        testMaterial = new Material();
        testMaterial.setMaterialId(1L);
        testMaterial.setMaterialName("机油");
        testMaterial.setCategory("润滑油");
        testMaterial.setSpecification("5W-30");
        testMaterial.setUnit("升");
        testMaterial.setUnitPrice(new BigDecimal("50.00"));
        testMaterial.setInventory(100);
        testMaterial.setStatus(1);
        testMaterial.setCreateTime(LocalDateTime.now());
        testMaterial.setUpdateTime(LocalDateTime.now());

        // 创建材料请求
        materialRequest = new MaterialRequest();
        materialRequest.setMaterialName("刹车片");
        materialRequest.setCategory("制动系统");
        materialRequest.setSpecification("前轮");
        materialRequest.setUnit("套");
        materialRequest.setUnitPrice(new BigDecimal("200.00"));
        materialRequest.setInventory(50);
    }

    @Test
    void testGetMaterials_Success() {
        // Given
        List<Material> materials = Arrays.asList(testMaterial);
        Page<Material> materialPage = new PageImpl<>(materials);
        when(materialRepository.findByStatus(eq(1), any(Pageable.class)))
                .thenReturn(materialPage);

        // When
        PageResponse<MaterialDTO> result = materialService.getMaterials(null, null, PageRequest.of(0, 10));

        // Then
        assertNotNull(result);
        assertEquals(1, result.getData().getContent().size());
        assertEquals(testMaterial.getMaterialId(), result.getData().getContent().get(0).getMaterialId());
        assertEquals(testMaterial.getMaterialName(), result.getData().getContent().get(0).getMaterialName());

        verify(materialRepository).findByStatus(eq(1), any(Pageable.class));
    }

    @Test
    void testGetMaterials_WithSearchAndCategory() {
        // Given
        List<Material> materials = Arrays.asList(testMaterial);
        Page<Material> materialPage = new PageImpl<>(materials);
        when(materialRepository.findByMaterialNameContainingAndCategoryAndStatus(
                eq("机油"), eq("润滑油"), eq(1), any(Pageable.class)))
                .thenReturn(materialPage);

        // When
        PageResponse<MaterialDTO> result = materialService.getMaterials("机油", "润滑油", PageRequest.of(0, 10));

        // Then
        assertNotNull(result);
        assertEquals(1, result.getData().getContent().size());

        verify(materialRepository).findByMaterialNameContainingAndCategoryAndStatus(
                eq("机油"), eq("润滑油"), eq(1), any(Pageable.class));
    }

    @Test
    void testGetMaterial_Success() {
        // Given
        when(materialRepository.findById(1L)).thenReturn(Optional.of(testMaterial));

        // When
        MaterialDTO result = materialService.getMaterial(1L);

        // Then
        assertNotNull(result);
        assertEquals(testMaterial.getMaterialId(), result.getMaterialId());
        assertEquals(testMaterial.getMaterialName(), result.getMaterialName());
        assertEquals(testMaterial.getCategory(), result.getCategory());

        verify(materialRepository).findById(1L);
    }

    @Test
    void testGetMaterial_MaterialNotFound() {
        // Given
        when(materialRepository.findById(1L)).thenReturn(Optional.empty());

        // When & Then
        ResourceNotFoundException exception = assertThrows(ResourceNotFoundException.class,
                () -> materialService.getMaterial(1L));
        assertEquals("材料不存在", exception.getMessage());

        verify(materialRepository).findById(1L);
    }

    @Test
    void testAddMaterial_Success() {
        // Given
        when(materialRepository.existsByMaterialNameAndSpecification(anyString(), anyString()))
                .thenReturn(false);
        when(materialRepository.save(any(Material.class))).thenReturn(testMaterial);

        // When
        MaterialDTO result = materialService.addMaterial(materialRequest);

        // Then
        assertNotNull(result);
        assertEquals(testMaterial.getMaterialId(), result.getMaterialId());
        assertEquals(testMaterial.getMaterialName(), result.getMaterialName());

        verify(materialRepository).existsByMaterialNameAndSpecification("刹车片", "前轮");
        verify(materialRepository).save(any(Material.class));
    }

    @Test
    void testAddMaterial_MaterialExists() {
        // Given
        when(materialRepository.existsByMaterialNameAndSpecification(anyString(), anyString()))
                .thenReturn(true);

        // When & Then
        BusinessException exception = assertThrows(BusinessException.class,
                () -> materialService.addMaterial(materialRequest));
        assertEquals("相同名称和规格的材料已存在", exception.getMessage());

        verify(materialRepository).existsByMaterialNameAndSpecification("刹车片", "前轮");
        verify(materialRepository, never()).save(any(Material.class));
    }

    @Test
    void testUpdateMaterial_Success() {
        // Given
        when(materialRepository.findById(1L)).thenReturn(Optional.of(testMaterial));
        when(materialRepository.save(any(Material.class))).thenReturn(testMaterial);

        // When
        MaterialDTO result = materialService.updateMaterial(1L, materialRequest);

        // Then
        assertNotNull(result);
        verify(materialRepository).findById(1L);
        verify(materialRepository).save(any(Material.class));
    }

    @Test
    void testUpdateMaterial_MaterialNotFound() {
        // Given
        when(materialRepository.findById(1L)).thenReturn(Optional.empty());

        // When & Then
        ResourceNotFoundException exception = assertThrows(ResourceNotFoundException.class,
                () -> materialService.updateMaterial(1L, materialRequest));
        assertEquals("材料不存在", exception.getMessage());

        verify(materialRepository).findById(1L);
        verify(materialRepository, never()).save(any(Material.class));
    }

    @Test
    void testUpdateMaterial_NameAndSpecificationExists() {
        // Given
        Material differentMaterial = new Material();
        differentMaterial.setMaterialId(1L);
        differentMaterial.setMaterialName("不同材料");
        differentMaterial.setSpecification("不同规格");

        when(materialRepository.findById(1L)).thenReturn(Optional.of(differentMaterial));
        when(materialRepository.existsByMaterialNameAndSpecification(
                anyString(), anyString())).thenReturn(true);

        // When & Then
        BusinessException exception = assertThrows(BusinessException.class,
                () -> materialService.updateMaterial(1L, materialRequest));
        assertEquals("相同名称和规格的材料已存在", exception.getMessage());

        verify(materialRepository).findById(1L);
        verify(materialRepository, never()).save(any(Material.class));
    }

    @Test
    void testDeleteMaterial_Success() {
        // Given
        when(materialRepository.findById(1L)).thenReturn(Optional.of(testMaterial));
        when(materialRepository.save(any(Material.class))).thenReturn(testMaterial);

        // When
        materialService.deleteMaterial(1L);

        // Then
        verify(materialRepository).findById(1L);
        verify(materialRepository).save(testMaterial); // 软删除，设置status=0
    }

    @Test
    void testDeleteMaterial_MaterialNotFound() {
        // Given
        when(materialRepository.findById(1L)).thenReturn(Optional.empty());

        // When & Then
        ResourceNotFoundException exception = assertThrows(ResourceNotFoundException.class,
                () -> materialService.deleteMaterial(1L));
        assertEquals("材料不存在", exception.getMessage());

        verify(materialRepository).findById(1L);
        verify(materialRepository, never()).delete(any(Material.class));
    }

    @Test
    void testGetCategories_Success() {
        // Given
        List<String> categories = Arrays.asList("润滑油", "制动系统", "电气系统");
        when(materialRepository.findDistinctCategories()).thenReturn(categories);

        // When
        List<String> result = materialService.getCategories();

        // Then
        assertNotNull(result);
        assertEquals(3, result.size());
        assertTrue(result.contains("润滑油"));
        assertTrue(result.contains("制动系统"));
        assertTrue(result.contains("电气系统"));

        verify(materialRepository).findDistinctCategories();
    }


}
