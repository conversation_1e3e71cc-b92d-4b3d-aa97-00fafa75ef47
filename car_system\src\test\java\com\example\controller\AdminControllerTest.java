package com.example.controller;

import com.example.config.TestSecurityConfig;
import com.example.dto.request.UserRegistrationRequest;
import com.example.dto.response.UserDTO;
import com.example.dto.response.TechnicianDTO;
import com.example.dto.response.VehicleDTO;
import com.example.dto.response.OrderDTO;
import com.example.dto.response.PageResponse;
import com.example.service.AdminService;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.context.annotation.Import;
import org.springframework.http.MediaType;
import org.springframework.security.test.context.support.WithMockUser;
import org.springframework.test.web.servlet.MockMvc;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;
import static org.springframework.security.test.web.servlet.request.SecurityMockMvcRequestPostProcessors.csrf;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

@WebMvcTest(AdminController.class)
@Import(TestSecurityConfig.class)
class AdminControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @MockBean
    private AdminService adminService;

    @Autowired
    private ObjectMapper objectMapper;

    private UserRegistrationRequest adminRequest;
    private UserDTO userDTO;
    private TechnicianDTO technicianDTO;
    private VehicleDTO vehicleDTO;
    private OrderDTO orderDTO;

    @BeforeEach
    void setUp() {
        // 创建管理员注册请求
        adminRequest = new UserRegistrationRequest();
        adminRequest.setUsername("admin001");
        adminRequest.setPassword("password123");
        adminRequest.setRealName("Admin User");
        adminRequest.setPhone("13800138001");
        adminRequest.setEmail("<EMAIL>");
        adminRequest.setAddress("Admin Address");

        // 创建用户DTO
        userDTO = new UserDTO(
                1L, "testuser", "Test User", "13800138000",
                "<EMAIL>", "Test Address", 1, "user",
                LocalDateTime.now(), LocalDateTime.now()
        );

        // 创建技师DTO
        technicianDTO = new TechnicianDTO();
        technicianDTO.setTechnicianId(1L);
        technicianDTO.setUsername("tech001");
        technicianDTO.setRealName("Technician One");
        technicianDTO.setPhone("13800138002");
        technicianDTO.setEmail("<EMAIL>");
        technicianDTO.setSpecialty("engine");
        technicianDTO.setSpecialtyName("发动机维修");
        technicianDTO.setHourlyRate(BigDecimal.valueOf(50.0));
        technicianDTO.setStatus(1);
        technicianDTO.setCreateTime(LocalDateTime.now());
        technicianDTO.setUpdateTime(LocalDateTime.now());

        // 创建车辆DTO
        vehicleDTO = new VehicleDTO(
                1L, 1L, "京A12345", "Toyota", "Camry",
                2020, "1HGBH41JXMN109186", "白色", "ENG123456",
                LocalDate.of(2020, 1, 1), LocalDateTime.now(), LocalDateTime.now()
        );

        // 创建工单DTO
        orderDTO = new OrderDTO();
        orderDTO.setOrderId(1L);
        orderDTO.setStatus("pending");
        orderDTO.setSubmitTime(LocalDateTime.now());
    }

    @Test
    @WithMockUser(roles = "ADMIN")
    void testCreateAdministrator_Success() throws Exception {
        // Given
        UserDTO adminDTO = new UserDTO(
                2L, "admin001", "Admin User", "13800138001",
                "<EMAIL>", "Admin Address", 1, "admin",
                LocalDateTime.now(), LocalDateTime.now()
        );
        when(adminService.createAdministrator(any(UserRegistrationRequest.class))).thenReturn(adminDTO);

        // When & Then
        mockMvc.perform(post("/admin/administrators")
                        .with(csrf())
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(adminRequest)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.message").value("管理员账户创建成功"))
                .andExpect(jsonPath("$.data.userId").value(2))
                .andExpect(jsonPath("$.data.username").value("admin001"))
                .andExpect(jsonPath("$.data.userType").value("admin"));

        verify(adminService).createAdministrator(any(UserRegistrationRequest.class));
    }

    @Test
    @WithMockUser(roles = "ADMIN")
    void testGetAllUsers_Success() throws Exception {
        // Given
        List<UserDTO> users = Arrays.asList(userDTO);
        PageResponse<UserDTO> pageResponse = PageResponse.success(users,
                new PageResponse.PageInfo(1, 20, 1L, 1));
        when(adminService.getAllUsers(isNull(), isNull(), any())).thenReturn(pageResponse);

        // When & Then
        mockMvc.perform(get("/admin/users"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.message").value("获取用户列表成功"))
                .andExpect(jsonPath("$.data.data").isArray())
                .andExpect(jsonPath("$.data.data[0].userId").value(1))
                .andExpect(jsonPath("$.data.pageInfo.currentPage").value(1));

        verify(adminService).getAllUsers(isNull(), isNull(), any());
    }

    @Test
    @WithMockUser(roles = "ADMIN")
    void testGetAllUsers_WithFilters() throws Exception {
        // Given
        List<UserDTO> users = Arrays.asList(userDTO);
        PageResponse<UserDTO> pageResponse = PageResponse.success(users,
                new PageResponse.PageInfo(1, 10, 1L, 1));
        when(adminService.getAllUsers(eq("test"), eq(1), any())).thenReturn(pageResponse);

        // When & Then
        mockMvc.perform(get("/admin/users")
                        .param("page", "1")
                        .param("size", "10")
                        .param("search", "test")
                        .param("status", "1"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data.pageInfo.pageSize").value(10));

        verify(adminService).getAllUsers(eq("test"), eq(1), any());
    }

    @Test
    @WithMockUser(roles = "ADMIN")
    void testGetAllTechnicians_Success() throws Exception {
        // Given
        List<TechnicianDTO> technicians = Arrays.asList(technicianDTO);
        PageResponse<TechnicianDTO> pageResponse = PageResponse.success(technicians,
                new PageResponse.PageInfo(1, 20, 1L, 1));
        when(adminService.getAllTechnicians(isNull(), isNull(), any())).thenReturn(pageResponse);

        // When & Then
        mockMvc.perform(get("/admin/technicians"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.message").value("获取技师列表成功"))
                .andExpect(jsonPath("$.data.data").isArray())
                .andExpect(jsonPath("$.data.data[0].technicianId").value(1));

        verify(adminService).getAllTechnicians(isNull(), isNull(), any());
    }

    @Test
    @WithMockUser(roles = "ADMIN")
    void testGetAllVehicles_Success() throws Exception {
        // Given
        List<VehicleDTO> vehicles = Arrays.asList(vehicleDTO);
        PageResponse<VehicleDTO> pageResponse = PageResponse.success(vehicles,
                new PageResponse.PageInfo(1, 20, 1L, 1));
        when(adminService.getAllVehicles(isNull(), isNull(), any())).thenReturn(pageResponse);

        // When & Then
        mockMvc.perform(get("/admin/vehicles"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.message").value("获取车辆列表成功"))
                .andExpect(jsonPath("$.data.data").isArray())
                .andExpect(jsonPath("$.data.data[0].vehicleId").value(1));

        verify(adminService).getAllVehicles(isNull(), isNull(), any());
    }

    @Test
    @WithMockUser(roles = "ADMIN")
    void testGetAllOrders_Success() throws Exception {
        // Given
        List<OrderDTO> orders = Arrays.asList(orderDTO);
        PageResponse<OrderDTO> pageResponse = PageResponse.success(orders,
                new PageResponse.PageInfo(1, 20, 1L, 1));
        when(adminService.getAllOrders(isNull(), isNull(), isNull(), isNull(), any())).thenReturn(pageResponse);

        // When & Then
        mockMvc.perform(get("/admin/orders"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.message").value("获取工单列表成功"))
                .andExpect(jsonPath("$.data.data").isArray())
                .andExpect(jsonPath("$.data.data[0].orderId").value(1));

        verify(adminService).getAllOrders(isNull(), isNull(), isNull(), isNull(), any());
    }

    @Test
    @WithMockUser(roles = "ADMIN")
    void testDeleteUser_Success() throws Exception {
        // Given
        doNothing().when(adminService).deleteUser(1L);

        // When & Then
        mockMvc.perform(delete("/admin/users/1")
                        .with(csrf()))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.message").value("删除用户成功"));

        verify(adminService).deleteUser(1L);
    }

    @Test
    @WithMockUser(roles = "ADMIN")
    void testDeleteTechnician_Success() throws Exception {
        // Given
        doNothing().when(adminService).deleteTechnician(1L);

        // When & Then
        mockMvc.perform(delete("/admin/technicians/1")
                        .with(csrf()))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.message").value("删除技师成功"));

        verify(adminService).deleteTechnician(1L);
    }

    @Test
    @WithMockUser(roles = "USER") // 错误的角色
    void testCreateAdministrator_Forbidden() throws Exception {
        // When & Then
        mockMvc.perform(post("/admin/administrators")
                        .with(csrf())
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(adminRequest)))
                .andExpect(status().isForbidden());

        verify(adminService, never()).createAdministrator(any(UserRegistrationRequest.class));
    }

    @Test
    void testGetAllUsers_Unauthorized() throws Exception {
        // When & Then
        mockMvc.perform(get("/admin/users"))
                .andExpect(status().isUnauthorized());

        verify(adminService, never()).getAllUsers(any(), any(), any());
    }

    @Test
    @WithMockUser(roles = "ADMIN")
    void testCreateAdministrator_InvalidInput() throws Exception {
        // Given
        adminRequest.setUsername(null); // 缺少必填字段

        // When & Then
        mockMvc.perform(post("/admin/administrators")
                        .with(csrf())
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(adminRequest)))
                .andExpect(status().isBadRequest());

        verify(adminService, never()).createAdministrator(any(UserRegistrationRequest.class));
    }
}
