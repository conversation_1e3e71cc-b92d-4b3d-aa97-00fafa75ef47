package com.example.service;

import com.example.dto.request.TechnicianRegistrationRequest;
import com.example.dto.response.TechnicianDTO;
import com.example.dto.response.SpecialtyDTO;
import com.example.dto.response.OrderDTO;
import com.example.dto.response.PageResponse;
import com.example.entity.Technician;
import com.example.entity.RepairOrder;
import com.example.exception.BusinessException;
import com.example.exception.ResourceNotFoundException;
import com.example.repository.TechnicianRepository;
import com.example.repository.RepairOrderRepository;
import com.example.config.AppProperties;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.security.crypto.password.PasswordEncoder;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class TechnicianServiceTest {

    @Mock
    private TechnicianRepository technicianRepository;

    @Mock
    private RepairOrderRepository repairOrderRepository;

    @Mock
    private PasswordEncoder passwordEncoder;

    @Mock
    private AppProperties appProperties;

    @InjectMocks
    private TechnicianService technicianService;

    private Technician testTechnician;
    private TechnicianRegistrationRequest registrationRequest;
    private RepairOrder testOrder;

    @BeforeEach
    void setUp() {
        // 创建测试技师
        testTechnician = new Technician();
        testTechnician.setTechnicianId(1L);
        testTechnician.setUsername("testtechnician");
        testTechnician.setPassword("encodedPassword");
        testTechnician.setRealName("Test Technician");
        testTechnician.setPhone("13800138000");
        testTechnician.setEmail("<EMAIL>");
        testTechnician.setSpecialty(Technician.Specialty.ENGINE);
        testTechnician.setHourlyRate(new BigDecimal("100.00"));
        testTechnician.setHireDate(LocalDate.now());
        testTechnician.setStatus(1);
        testTechnician.setWorkload(0);
        testTechnician.setRating(new BigDecimal("5.0"));
        testTechnician.setCreateTime(LocalDateTime.now());
        testTechnician.setUpdateTime(LocalDateTime.now());

        // 创建技师注册请求
        registrationRequest = new TechnicianRegistrationRequest();
        registrationRequest.setUsername("newtechnician");
        registrationRequest.setPassword("password123");
        registrationRequest.setRealName("New Technician");
        registrationRequest.setPhone("***********");
        registrationRequest.setEmail("<EMAIL>");
        registrationRequest.setSpecialty("engine");
        registrationRequest.setHourlyRate(new BigDecimal("120.00"));
        registrationRequest.setHireDate(LocalDate.now());

        // 创建测试工单
        testOrder = new RepairOrder();
        testOrder.setOrderId(1L);
        testOrder.setStatus(RepairOrder.OrderStatus.ASSIGNED);
        testOrder.setSubmitTime(LocalDateTime.now());
    }

    @Test
    void testRegisterTechnician_Success() {
        // Given
        when(technicianRepository.existsByUsername(anyString())).thenReturn(false);
        when(technicianRepository.existsByPhone(anyString())).thenReturn(false);
        when(technicianRepository.existsByEmail(anyString())).thenReturn(false);
        when(passwordEncoder.encode(anyString())).thenReturn("encodedPassword");
        when(technicianRepository.save(any(Technician.class))).thenReturn(testTechnician);

        // When
        TechnicianDTO result = technicianService.registerTechnician(registrationRequest);

        // Then
        assertNotNull(result);
        assertEquals(testTechnician.getTechnicianId(), result.getTechnicianId());
        assertEquals(testTechnician.getUsername(), result.getUsername());
        assertEquals(testTechnician.getRealName(), result.getRealName());
        assertEquals(testTechnician.getSpecialty().name().toLowerCase(), result.getSpecialty());

        verify(technicianRepository).existsByUsername("newtechnician");
        verify(technicianRepository).existsByPhone("***********");
        verify(technicianRepository).existsByEmail("<EMAIL>");
        verify(passwordEncoder).encode("password123");
        verify(technicianRepository).save(any(Technician.class));
    }

    @Test
    void testRegisterTechnician_UsernameExists() {
        // Given
        when(technicianRepository.existsByUsername(anyString())).thenReturn(true);

        // When & Then
        BusinessException exception = assertThrows(BusinessException.class,
                () -> technicianService.registerTechnician(registrationRequest));
        assertEquals("用户名已存在", exception.getMessage());

        verify(technicianRepository).existsByUsername("newtechnician");
        verify(technicianRepository, never()).save(any(Technician.class));
    }

    @Test
    void testRegisterTechnician_PhoneExists() {
        // Given
        when(technicianRepository.existsByUsername(anyString())).thenReturn(false);
        when(technicianRepository.existsByPhone(anyString())).thenReturn(true);

        // When & Then
        BusinessException exception = assertThrows(BusinessException.class,
                () -> technicianService.registerTechnician(registrationRequest));
        assertEquals("手机号已被注册", exception.getMessage());

        verify(technicianRepository).existsByPhone("***********");
        verify(technicianRepository, never()).save(any(Technician.class));
    }

    @Test
    void testRegisterTechnician_EmailExists() {
        // Given
        when(technicianRepository.existsByUsername(anyString())).thenReturn(false);
        when(technicianRepository.existsByPhone(anyString())).thenReturn(false);
        when(technicianRepository.existsByEmail(anyString())).thenReturn(true);

        // When & Then
        BusinessException exception = assertThrows(BusinessException.class,
                () -> technicianService.registerTechnician(registrationRequest));
        assertEquals("邮箱已被注册", exception.getMessage());

        verify(technicianRepository).existsByEmail("<EMAIL>");
        verify(technicianRepository, never()).save(any(Technician.class));
    }

    @Test
    void testRegisterTechnician_InvalidSpecialty() {
        // Given
        registrationRequest.setSpecialty("invalid_specialty");
        when(technicianRepository.existsByUsername(anyString())).thenReturn(false);
        when(technicianRepository.existsByPhone(anyString())).thenReturn(false);
        when(technicianRepository.existsByEmail(anyString())).thenReturn(false);

        // When & Then
        BusinessException exception = assertThrows(BusinessException.class,
                () -> technicianService.registerTechnician(registrationRequest));
        assertEquals("无效的工种类型", exception.getMessage());

        verify(technicianRepository, never()).save(any(Technician.class));
    }

    @Test
    void testGetSpecialties_Success() {
        // Given
        AppProperties.Specialty engineSpecialty = new AppProperties.Specialty();
        engineSpecialty.setCode("engine");
        engineSpecialty.setName("发动机维修");

        AppProperties.Specialty transmissionSpecialty = new AppProperties.Specialty();
        transmissionSpecialty.setCode("transmission");
        transmissionSpecialty.setName("变速箱维修");

        List<AppProperties.Specialty> specialties = Arrays.asList(
                engineSpecialty, transmissionSpecialty
        );
        when(appProperties.getSpecialties()).thenReturn(specialties);

        // When
        List<SpecialtyDTO> result = technicianService.getSpecialties();

        // Then
        assertNotNull(result);
        assertEquals(2, result.size());
        assertEquals("engine", result.get(0).getCode());
        assertEquals("发动机维修", result.get(0).getName());
        assertEquals("transmission", result.get(1).getCode());
        assertEquals("变速箱维修", result.get(1).getName());

        verify(appProperties).getSpecialties();
    }

    @Test
    void testGetCurrentTechnician_Success() {
        // Given
        when(technicianRepository.findById(1L)).thenReturn(Optional.of(testTechnician));

        // When
        TechnicianDTO result = technicianService.getCurrentTechnician(1L);

        // Then
        assertNotNull(result);
        assertEquals(testTechnician.getTechnicianId(), result.getTechnicianId());
        assertEquals(testTechnician.getUsername(), result.getUsername());
        assertEquals(testTechnician.getRealName(), result.getRealName());

        verify(technicianRepository).findById(1L);
    }

    @Test
    void testGetCurrentTechnician_TechnicianNotFound() {
        // Given
        when(technicianRepository.findById(1L)).thenReturn(Optional.empty());

        // When & Then
        ResourceNotFoundException exception = assertThrows(ResourceNotFoundException.class,
                () -> technicianService.getCurrentTechnician(1L));
        assertEquals("技师不存在", exception.getMessage());

        verify(technicianRepository).findById(1L);
    }

    @Test
    void testGetTechnicianOrders_Success() {
        // Given
        List<RepairOrder> orders = Arrays.asList(testOrder);
        Page<RepairOrder> orderPage = new PageImpl<>(orders);

        when(repairOrderRepository.findByAssignedTechniciansContaining(
                eq(1L), any(Pageable.class))).thenReturn(orderPage);
        when(technicianRepository.findById(1L)).thenReturn(Optional.of(testTechnician));

        // When
        PageResponse<OrderDTO> result = technicianService.getCurrentTechnicianOrders(1L, null, PageRequest.of(0, 10));

        // Then
        assertNotNull(result);
        assertEquals(1, result.getData().getContent().size());

        verify(repairOrderRepository).findByAssignedTechniciansContaining(
                eq(1L), any(Pageable.class));
    }

    @Test
    void testFindAvailableTechniciansBySpecialty_Success() {
        // Given
        List<Technician> technicians = Arrays.asList(testTechnician);
        when(technicianRepository.findAvailableTechniciansBySpecialty(Technician.Specialty.ENGINE))
                .thenReturn(technicians);

        // When
        List<TechnicianDTO> result = technicianService.findAvailableTechniciansBySpecialty("engine", 5);

        // Then
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals(testTechnician.getTechnicianId(), result.get(0).getTechnicianId());

        verify(technicianRepository).findAvailableTechniciansBySpecialty(Technician.Specialty.ENGINE);
    }

    @Test
    void testFindAvailableTechniciansBySpecialty_InvalidSpecialty() {
        // When & Then
        BusinessException exception = assertThrows(BusinessException.class,
                () -> technicianService.findAvailableTechniciansBySpecialty("invalid", 5));
        assertEquals("无效的工种类型", exception.getMessage());

        verify(technicianRepository, never()).findAvailableTechniciansBySpecialty(any());
    }

    @Test
    void testRegisterTechnician_EmptyEmail() {
        // Given
        registrationRequest.setEmail("");
        when(technicianRepository.existsByUsername(anyString())).thenReturn(false);
        when(technicianRepository.existsByPhone(anyString())).thenReturn(false);
        when(passwordEncoder.encode(anyString())).thenReturn("encodedPassword");
        when(technicianRepository.save(any(Technician.class))).thenReturn(testTechnician);

        // When
        TechnicianDTO result = technicianService.registerTechnician(registrationRequest);

        // Then
        assertNotNull(result);
        verify(technicianRepository, never()).existsByEmail(anyString());
        verify(technicianRepository).save(any(Technician.class));
    }

    @Test
    void testRegisterTechnician_NullEmail() {
        // Given
        registrationRequest.setEmail(null);
        when(technicianRepository.existsByUsername(anyString())).thenReturn(false);
        when(technicianRepository.existsByPhone(anyString())).thenReturn(false);
        when(passwordEncoder.encode(anyString())).thenReturn("encodedPassword");
        when(technicianRepository.save(any(Technician.class))).thenReturn(testTechnician);

        // When
        TechnicianDTO result = technicianService.registerTechnician(registrationRequest);

        // Then
        assertNotNull(result);
        verify(technicianRepository, never()).existsByEmail(anyString());
        verify(technicianRepository).save(any(Technician.class));
    }
}
