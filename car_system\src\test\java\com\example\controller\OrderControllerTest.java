package com.example.controller;

import com.example.config.TestSecurityConfig;
import com.example.dto.request.OrderRequest;
import com.example.dto.request.OrderCompletionRequest;
import com.example.dto.request.OrderFeedbackRequest;
import com.example.dto.request.OrderStatusUpdateRequest;
import com.example.dto.request.MaterialUsageRequest;
import com.example.dto.response.OrderDTO;
import com.example.security.JwtAuthenticationFilter;
import com.example.service.OrderService;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.context.annotation.Import;
import org.springframework.http.MediaType;
import org.springframework.security.test.context.support.WithMockUser;
import org.springframework.test.web.servlet.MockMvc;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;
import static org.springframework.security.test.web.servlet.request.SecurityMockMvcRequestPostProcessors.csrf;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

@WebMvcTest(OrderController.class)
@Import(TestSecurityConfig.class)
class OrderControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @MockBean
    private OrderService orderService;

    @Autowired
    private ObjectMapper objectMapper;

    private OrderRequest orderRequest;
    private OrderDTO orderDTO;
    private OrderFeedbackRequest feedbackRequest;
    private OrderStatusUpdateRequest statusUpdateRequest;
    private OrderCompletionRequest completionRequest;
    private MaterialUsageRequest materialUsageRequest;

    @BeforeEach
    void setUp() {
        // 创建工单请求
        orderRequest = new OrderRequest();
        orderRequest.setVehicleId(1L);
        orderRequest.setFaultTypeId(1L);
        orderRequest.setDescription("发动机异响");
        orderRequest.setUrgencyLevel("normal");
        orderRequest.setContactPhone("13800138000");

        // 创建工单DTO
        orderDTO = new OrderDTO();
        orderDTO.setOrderId(1L);
        orderDTO.setStatus("pending");
        orderDTO.setDescription("发动机异响");
        orderDTO.setUrgencyLevel("normal");
        orderDTO.setSubmitTime(LocalDateTime.now());

        // 创建反馈请求
        feedbackRequest = new OrderFeedbackRequest();
        feedbackRequest.setRating(5);
        feedbackRequest.setComment("服务很好");

        // 创建状态更新请求
        statusUpdateRequest = new OrderStatusUpdateRequest();
        statusUpdateRequest.setStatus("assigned");
        statusUpdateRequest.setReason("分配技师");

        // 创建完成请求
        completionRequest = new OrderCompletionRequest();
        completionRequest.setWorkingHours(new BigDecimal("4.5"));
        completionRequest.setWorkResult("发动机异响问题已解决");

        // 创建材料使用请求
        materialUsageRequest = new MaterialUsageRequest();
        materialUsageRequest.setMaterialId(1L);
        materialUsageRequest.setQuantity(new BigDecimal("2"));
        materialUsageRequest.setTotalPrice(new BigDecimal("100.00"));
    }

    @Test
    @WithMockUser(roles = "USER")
    void testSubmitOrder_Success() throws Exception {
        // Given
        when(orderService.submitOrder(eq(1L), any(OrderRequest.class))).thenReturn(orderDTO);

        // When & Then
        mockMvc.perform(post("/orders")
                        .with(csrf())
                        .requestAttr("userPrincipal", createUserPrincipal("user", "USER", 1L))
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(orderRequest)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.message").value("工单提交成功"))
                .andExpect(jsonPath("$.data.orderId").value(1))
                .andExpect(jsonPath("$.data.status").value("pending"));

        verify(orderService).submitOrder(eq(1L), any(OrderRequest.class));
    }

    @Test
    @WithMockUser(roles = "USER")
    void testGetOrder_Success() throws Exception {
        // Given
        when(orderService.getOrder(eq(1L), eq(1L), eq("USER"))).thenReturn(orderDTO);

        // When & Then
        mockMvc.perform(get("/orders/1")
                        .requestAttr("userPrincipal", createUserPrincipal("user", "USER", 1L)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.message").value("获取工单详情成功"))
                .andExpect(jsonPath("$.data.orderId").value(1))
                .andExpect(jsonPath("$.data.description").value("发动机异响"));

        verify(orderService).getOrder(eq(1L), eq(1L), eq("USER"));
    }

    @Test
    @WithMockUser(roles = "ADMIN")
    void testUpdateOrderStatus_Success() throws Exception {
        // Given
        doNothing().when(orderService).updateOrderStatus(eq(1L), any(OrderStatusUpdateRequest.class));

        // When & Then
        mockMvc.perform(patch("/orders/1/status")
                        .with(csrf())
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(statusUpdateRequest)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.message").value("工单状态更新成功"));

        verify(orderService).updateOrderStatus(eq(1L), any(OrderStatusUpdateRequest.class));
    }

    @Test
    @WithMockUser(roles = "USER")
    void testSubmitFeedback_Success() throws Exception {
        // Given
        doNothing().when(orderService).submitFeedback(eq(1L), eq(1L), any(OrderFeedbackRequest.class));

        // When & Then
        mockMvc.perform(post("/orders/1/feedback")
                        .with(csrf())
                        .requestAttr("userPrincipal", createUserPrincipal("user", "USER", 1L))
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(feedbackRequest)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.message").value("反馈提交成功"));

        verify(orderService).submitFeedback(eq(1L), eq(1L), any(OrderFeedbackRequest.class));
    }

    @Test
    @WithMockUser(roles = "TECHNICIAN")
    void testAcceptOrder_Success() throws Exception {
        // Given
        doNothing().when(orderService).acceptOrder(eq(1L), eq(1L));

        // When & Then
        mockMvc.perform(patch("/orders/1/accept")
                        .with(csrf())
                        .requestAttr("userPrincipal", createUserPrincipal("tech", "TECHNICIAN", 1L)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.message").value("工单接受成功"));

        verify(orderService).acceptOrder(eq(1L), eq(1L));
    }

    @Test
    @WithMockUser(roles = "TECHNICIAN")
    void testRejectOrder_Success() throws Exception {
        // Given
        OrderController.RejectRequest rejectRequest = new OrderController.RejectRequest();
        rejectRequest.setReason("时间冲突");
        doNothing().when(orderService).rejectOrder(eq(1L), eq(1L), eq("时间冲突"));

        // When & Then
        mockMvc.perform(patch("/orders/1/reject")
                        .with(csrf())
                        .requestAttr("userPrincipal", createUserPrincipal("tech", "TECHNICIAN", 1L))
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(rejectRequest)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.message").value("工单拒绝成功"));

        verify(orderService).rejectOrder(eq(1L), eq(1L), eq("时间冲突"));
    }

    @Test
    @WithMockUser(roles = "TECHNICIAN")
    void testStartWork_Success() throws Exception {
        // Given
        doNothing().when(orderService).startWork(eq(1L), eq(1L));

        // When & Then
        mockMvc.perform(patch("/orders/1/start")
                        .with(csrf())
                        .requestAttr("userPrincipal", createUserPrincipal("tech", "TECHNICIAN", 1L)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.message").value("开始维修工作"));

        verify(orderService).startWork(eq(1L), eq(1L));
    }

    @Test
    @WithMockUser(roles = "TECHNICIAN")
    void testCompleteWork_Success() throws Exception {
        // Given
        doNothing().when(orderService).completeWork(eq(1L), eq(1L), any(OrderCompletionRequest.class));

        // When & Then
        mockMvc.perform(patch("/orders/1/complete")
                        .with(csrf())
                        .requestAttr("userPrincipal", createUserPrincipal("tech", "TECHNICIAN", 1L))
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(completionRequest)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.message").value("维修工作完成"));

        verify(orderService).completeWork(eq(1L), eq(1L), any(OrderCompletionRequest.class));
    }

    @Test
    @WithMockUser(roles = "TECHNICIAN")
    void testAddMaterialUsage_Success() throws Exception {
        // Given
        doNothing().when(orderService).addMaterialUsage(eq(1L), eq(1L), any(MaterialUsageRequest.class));

        // When & Then
        mockMvc.perform(post("/orders/1/materials")
                        .with(csrf())
                        .requestAttr("userPrincipal", createUserPrincipal("tech", "TECHNICIAN", 1L))
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(materialUsageRequest)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.message").value("材料使用记录添加成功"));

        verify(orderService).addMaterialUsage(eq(1L), eq(1L), any(MaterialUsageRequest.class));
    }

    @Test
    @WithMockUser(roles = "USER")
    void testGetOrderHistory_Success() throws Exception {
        // Given
        List<Object> history = Arrays.asList("历史记录1", "历史记录2");
        when(orderService.getOrderHistory(eq(1L), eq(1L), eq("USER"))).thenReturn(history);

        // When & Then
        mockMvc.perform(get("/orders/1/history")
                        .requestAttr("userPrincipal", createUserPrincipal("user", "USER", 1L)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.message").value("获取工单历史成功"))
                .andExpect(jsonPath("$.data").isArray());

        verify(orderService).getOrderHistory(eq(1L), eq(1L), eq("USER"));
    }

    @Test
    @WithMockUser(roles = "USER")
    void testGetOrderMaterials_Success() throws Exception {
        // Given
        List<Object> materials = Arrays.asList("材料1", "材料2");
        when(orderService.getOrderMaterials(eq(1L), eq(1L), eq("USER"))).thenReturn(materials);

        // When & Then
        mockMvc.perform(get("/orders/1/materials")
                        .requestAttr("userPrincipal", createUserPrincipal("user", "USER", 1L)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.message").value("获取材料使用记录成功"))
                .andExpect(jsonPath("$.data").isArray());

        verify(orderService).getOrderMaterials(eq(1L), eq(1L), eq("USER"));
    }

    // 权限测试
    @Test
    @WithMockUser(roles = "TECHNICIAN") // 错误的角色
    void testSubmitOrder_Forbidden() throws Exception {
        // When & Then
        mockMvc.perform(post("/orders")
                        .with(csrf())
                        .requestAttr("userPrincipal", createUserPrincipal("tech", "TECHNICIAN", 1L))
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(orderRequest)))
                .andExpect(status().isForbidden());

        verify(orderService, never()).submitOrder(anyLong(), any(OrderRequest.class));
    }

    @Test
    @WithMockUser(roles = "USER") // 错误的角色
    void testUpdateOrderStatus_Forbidden() throws Exception {
        // When & Then
        mockMvc.perform(patch("/orders/1/status")
                        .with(csrf())
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(statusUpdateRequest)))
                .andExpect(status().isForbidden());

        verify(orderService, never()).updateOrderStatus(anyLong(), any(OrderStatusUpdateRequest.class));
    }

    @Test
    @WithMockUser(roles = "USER") // 错误的角色
    void testAcceptOrder_Forbidden() throws Exception {
        // When & Then
        mockMvc.perform(patch("/orders/1/accept")
                        .with(csrf())
                        .requestAttr("userPrincipal", createUserPrincipal("user", "USER", 1L)))
                .andExpect(status().isForbidden());

        verify(orderService, never()).acceptOrder(anyLong(), anyLong());
    }

    @Test
    void testSubmitOrder_Unauthorized() throws Exception {
        // When & Then
        mockMvc.perform(post("/orders")
                        .with(csrf())
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(orderRequest)))
                .andExpect(status().isUnauthorized());

        verify(orderService, never()).submitOrder(anyLong(), any(OrderRequest.class));
    }

    // 输入验证测试
    @Test
    @WithMockUser(roles = "USER")
    void testSubmitOrder_InvalidInput() throws Exception {
        // Given
        orderRequest.setVehicleId(null); // 缺少必填字段

        // When & Then
        mockMvc.perform(post("/orders")
                        .with(csrf())
                        .requestAttr("userPrincipal", createUserPrincipal("user", "USER", 1L))
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(orderRequest)))
                .andExpect(status().isBadRequest());

        verify(orderService, never()).submitOrder(anyLong(), any(OrderRequest.class));
    }

    @Test
    @WithMockUser(roles = "USER")
    void testSubmitFeedback_InvalidRating() throws Exception {
        // Given
        feedbackRequest.setRating(6); // 超出范围

        // When & Then
        mockMvc.perform(post("/orders/1/feedback")
                        .with(csrf())
                        .requestAttr("userPrincipal", createUserPrincipal("user", "USER", 1L))
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(feedbackRequest)))
                .andExpect(status().isBadRequest());

        verify(orderService, never()).submitFeedback(anyLong(), anyLong(), any(OrderFeedbackRequest.class));
    }

    @Test
    @WithMockUser(roles = "ADMIN")
    void testUpdateOrderStatus_InvalidStatus() throws Exception {
        // Given
        statusUpdateRequest.setStatus("invalid_status");

        // When & Then
        mockMvc.perform(patch("/orders/1/status")
                        .with(csrf())
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(statusUpdateRequest)))
                .andExpect(status().isBadRequest());

        verify(orderService, never()).updateOrderStatus(anyLong(), any(OrderStatusUpdateRequest.class));
    }

    @Test
    @WithMockUser(roles = "TECHNICIAN")
    void testCompleteWork_InvalidWorkingHours() throws Exception {
        // Given
        completionRequest.setWorkingHours(new BigDecimal("-1")); // 负数

        // When & Then
        mockMvc.perform(patch("/orders/1/complete")
                        .with(csrf())
                        .requestAttr("userPrincipal", createUserPrincipal("tech", "TECHNICIAN", 1L))
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(completionRequest)))
                .andExpect(status().isBadRequest());

        verify(orderService, never()).completeWork(anyLong(), anyLong(), any(OrderCompletionRequest.class));
    }

    @Test
    void testGetOrder_InvalidId() throws Exception {
        // When & Then
        mockMvc.perform(get("/orders/invalid")
                        .requestAttr("userPrincipal", createUserPrincipal("user", "USER", 1L)))
                .andExpect(status().isBadRequest());

        verify(orderService, never()).getOrder(anyLong(), anyLong(), anyString());
    }

    /**
     * 创建UserPrincipal对象的helper方法
     */
    private JwtAuthenticationFilter.UserPrincipal createUserPrincipal(String username, String userType, Long userId) {
        return new JwtAuthenticationFilter.UserPrincipal(username, userType, userId);
    }
}
